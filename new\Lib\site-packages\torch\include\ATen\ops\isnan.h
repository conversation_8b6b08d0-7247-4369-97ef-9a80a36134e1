#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/isnan_ops.h>

namespace at {


// aten::isnan(Tensor self) -> Tensor
inline at::Tensor isnan(const at::Tensor & self) {
    return at::_ops::isnan::call(self);
}

// aten::isnan.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & isnan_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::isnan_out::call(self, out);
}
// aten::isnan.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & isnan_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::isnan_out::call(self, out);
}

}
