import sys
import logging
from typing import Union, List, Dict, Any, Optional, Tuple, Sequence
from pathlib import Path
import cv2
import os
import numpy as np
import torch
import torchvision.transforms as T
from huggingface_hub import snapshot_download
from transformers import RTDetrForObjectDetection, RTDetrImageProcessor
from PIL import Image , ImageDraw
from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.layout_analysis.base import BaseLayoutDetector, BaseLayoutMapper
from omnidocs.tasks.layout_analysis.enums import LayoutLabel
from omnidocs.tasks.layout_analysis.models import LayoutBox, LayoutOutput


logger = get_logger(__name__)

# ================================================================================================================


class RTDETRLayoutMapper(BaseLayoutMapper):
    """Label mapper for RT-DETR layout detection model."""
    
    def _setup_mapping(self):
        mapping = {
            "caption": LayoutLabel.CAPTION,
            "footnote": LayoutLabel.TEXT,  # Map footnote to text
            "formula": LayoutLabel.FORMULA,
            "list-item": LayoutLabel.LIST,
            "page-footer": LayoutLabel.TEXT,  # Map footer to text
            "page-header": LayoutLabel.TEXT,  # Map header to text
            "picture": LayoutLabel.IMAGE,
            "section-header": LayoutLabel.TITLE,  # Map section header to title
            "table": LayoutLabel.TABLE,
            "text": LayoutLabel.TEXT,
            "title": LayoutLabel.TITLE
        }
        self._mapping = {k.lower(): v for k, v in mapping.items()}
        self._reverse_mapping = {v: k for k, v in mapping.items()}

class RTDETRLayoutDetector(BaseLayoutDetector):
    """RT-DETR-based layout detection implementation."""

    MODEL_REPO = "HuggingPanda/docling-layout"
    DEFAULT_LOCAL_DIR = "./models/RTDETR-Layout"
    
    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        local_dir: Optional[Union[str, Path]] = None,
        num_threads: Optional[int] = 4,
        use_cpu_only: bool = True
    ):
        """Initialize RT-DETR Layout Detector with careful device handling."""
        super().__init__(show_log=show_log)
        
        self._label_mapper = RTDETRLayoutMapper()
        
        if self.show_log:
            logger.info("Initializing RTDETRLayoutDetector")
        
        
        """
        
        TODO:Fix GPU based inference
        
        GPU mode not working because of tensors not being on the same device
        
        
        """
        # Careful device handling
        if use_cpu_only:
            self.device = "cpu"
            if self.show_log:
                logger.info("Forced CPU usage due to use_cpu_only flag")
        elif device:
            self.device = device
            if self.show_log:
                logger.info(f"Using specified device: {device}")
        else:
            # Check CUDA availability with error handling
            try:
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
                if self.show_log:
                    logger.info(f"Automatically selected device: {self.device}")
            except Exception as e:
                self.device = "cpu"
                if self.show_log:
                    logger.warning(f"Error checking CUDA availability: {e}. Defaulting to CPU")
        
        self.local_dir = Path(local_dir) if local_dir else Path(self.DEFAULT_LOCAL_DIR)
        self.num_threads = num_threads or int(os.environ.get("OMP_NUM_THREADS", 4))
        
        # Set thread count for CPU operations
        if self.device == "cpu":
            torch.set_num_threads(self.num_threads)
            if self.show_log:
                logger.info(f"Set CPU threads to {self.num_threads}")
        
        # Model parameters
        self.image_size = 640
        self.confidence_threshold = 0.6
        
        try:
            self._download_model()  # Download model first
            self._load_model()
            if self.show_log:
                logger.success("Model initialized successfully")
        except Exception as e:
            if self.show_log:
                logger.error("Failed to initialize model", exc_info=True)
            raise

    @log_execution_time
    def _download_model(self) -> None:
        """Download RT-DETR model from HuggingFace Hub."""
        try:
            if self.show_log:
                logger.info(f"Preparing to load model from {self.MODEL_REPO}")
            
            # For Transformers models, we don't need to manually download
            # The model will be downloaded automatically when we call from_pretrained()
            # Just set a flag to indicate the model is ready for loading
            self._model_ready = True
            
            if self.show_log:
                logger.success("Model download preparation completed")
                
        except Exception as e:
            if self.show_log:
                logger.error(f"Failed to prepare model download: {e}", exc_info=True)
            raise RuntimeError(f"Model download preparation failed: {e}")

    @log_execution_time
    def _load_model(self) -> None:
        """Load RT-DETR model using Transformers library."""
        try:
            if not hasattr(self, '_model_ready') or not self._model_ready:
                raise RuntimeError("Model download not completed")
            
            if self.show_log:
                logger.info(f"Loading RT-DETR model from {self.MODEL_REPO}")
            
            # Load the image processor and model using Transformers
            try:
                self.image_processor = RTDetrImageProcessor.from_pretrained(self.MODEL_REPO)
                self.model = RTDetrForObjectDetection.from_pretrained(self.MODEL_REPO)
                
                if self.show_log:
                    logger.info("Model and processor loaded successfully")
                
                # Move model to the correct device
                if self.device == "cuda" and torch.cuda.is_available():
                    try:
                        self.model = self.model.cuda()
                        if self.show_log:
                            logger.info("Model successfully moved to CUDA")
                    except Exception as e:
                        self.device = "cpu"  # Fallback to CPU
                        if self.show_log:
                            logger.warning(f"Failed to move model to CUDA: {e}. Falling back to CPU")
                else:
                    self.model = self.model.cpu()
                
            except Exception as e:
                raise RuntimeError(f"Failed to load model from HuggingFace: {e}")
            
            # Set model to evaluation mode
            self.model.eval()
            
            if self.show_log:
                logger.info(f"Model ready on device: {self.device}")
            
        except Exception as e:
            if self.show_log:
                logger.error("Error during model loading", exc_info=True)
            raise

    @log_execution_time
    def detect(
        self,
        input_path: Union[str, Path],
        confidence_threshold: Optional[float] = None,
        **kwargs
    ) -> Tuple[Image.Image, LayoutOutput]:
        """Run layout detection using RT-DETR Transformers model."""
        if self.model is None:
            raise RuntimeError("Model not loaded. Initialization failed.")

        try:
            # Load and preprocess image
            if isinstance(input_path, (str, Path)):
                image = Image.open(input_path).convert("RGB")
            elif isinstance(input_path, Image.Image):
                image = input_path.convert("RGB")
            elif isinstance(input_path, np.ndarray):
                image = Image.fromarray(input_path).convert("RGB")
            else:
                raise ValueError("Unsupported input type")

            # Preprocess the image using the image processor
            resize = {"height": self.image_size, "width": self.image_size}
            inputs = self.image_processor(
                images=image,
                return_tensors="pt",
                size=resize,
            )
            
            # Move inputs to the correct device
            if self.device == "cuda":
                inputs = {k: v.cuda() if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}

            # Run inference
            try:
                with torch.no_grad():
                    outputs = self.model(**inputs)
            except Exception as e:
                raise RuntimeError(f"Error during model inference: {e}")

            # Post-process results
            threshold = confidence_threshold or self.confidence_threshold
            results = self.image_processor.post_process_object_detection(
                outputs,
                target_sizes=torch.tensor([image.size[::-1]]),
                threshold=threshold
            )

            # Process predictions
            layout_boxes = []
            
            for result in results:
                for score, label_id, box in zip(result["scores"], result["labels"], result["boxes"]):
                    score_val = float(score.item())
                    label_idx = int(label_id.item())
                    
                    # Get label from model config (add 1 because model config is 0-indexed)
                    model_label = self.model.config.id2label.get(label_idx + 1)
                    if not model_label:
                        continue

                    # Map to standardized label
                    mapped_label = self.map_label(model_label)
                    if not mapped_label:
                        continue

                    # Convert box coordinates (already in image space)
                    box = [round(i, 2) for i in box.tolist()]
                    l, t, r, b = box

                    layout_boxes.append(
                        LayoutBox(
                            label=mapped_label,
                            bbox=[l, t, r, b],
                            confidence=score_val
                        )
                    )

            # Create annotated image
            annotated_img = image.copy()
            draw = ImageDraw.Draw(annotated_img)

            # Draw boxes with standardized colors
            for box in layout_boxes:
                color = self.color_map.get(box.label, 'gray')
                coords = box.bbox
                draw.rectangle(coords, outline=color, width=3)
                draw.text((coords[0], coords[1]-20), box.label, fill=color)

            return annotated_img, LayoutOutput(bboxes=layout_boxes)

        except Exception as e:
            if self.show_log:
                logger.error("Error during prediction", exc_info=True)
            raise