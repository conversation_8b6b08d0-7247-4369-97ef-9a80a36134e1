#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/special_airy_ai_ops.h>

namespace at {


// aten::special_airy_ai(Tensor x) -> Tensor
inline at::Tensor special_airy_ai(const at::Tensor & x) {
    return at::_ops::special_airy_ai::call(x);
}

// aten::special_airy_ai.out(Tensor x, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_airy_ai_out(at::Tensor & out, const at::Tensor & x) {
    return at::_ops::special_airy_ai_out::call(x, out);
}
// aten::special_airy_ai.out(Tensor x, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_airy_ai_outf(const at::Tensor & x, at::Tensor & out) {
    return at::_ops::special_airy_ai_out::call(x, out);
}

}
