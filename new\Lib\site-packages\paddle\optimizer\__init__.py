#   Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from . import lr  # noqa: F401
from .adadelta import Adadelta
from .adagrad import Adagrad
from .adam import Adam
from .adamax import Adamax
from .adamw import <PERSON><PERSON>
from .lamb import Lamb
from .lbfgs import <PERSON>BFGS
from .momentum import Momentum
from .optimizer import Optimizer
from .rmsprop import RMSProp
from .sgd import SGD

__all__ = [
    'Optimizer',
    'Adagrad',
    'Adam',
    'Adam<PERSON>',
    '<PERSON><PERSON>',
    'RMS<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'SG<PERSON>',
    'Momentum',
    '<PERSON>',
    'LBFGS',
]
