#pragma once

// @generated by torchgen/gen.py from NativeMetaFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/TensorIterator.h>
#include <ATen/TensorMeta.h>
#include <tuple>
#include <vector>

namespace at {
namespace meta {

struct TORCH_API structured_all_dim : public at::impl::MetaBase {
    
    
    void meta(const at::Tensor & self, int64_t dim, bool keepdim);
};
struct TORCH_API structured_all_dims : public at::impl::MetaBase {
    
    
    void meta(const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim);
};
struct TORCH_API structured_all : public at::impl::MetaBase {
    
    
    void meta(const at::Tensor & self);
};

} // namespace native
} // namespace at
