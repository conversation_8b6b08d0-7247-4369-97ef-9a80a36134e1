#!/usr/bin/env python3
"""
Comprehensive test file for all layout detectors.
Tests which layout detectors are working and demonstrates their functionality.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from omnidocs.utils.logging import get_logger

logger = get_logger(__name__)

def test_layout_detector(detector_class, detector_name, test_image_path, output_dir):
    """Test a single layout detector."""
    print(f"\n{'='*60}")
    print(f"Testing {detector_name}")
    print(f"{'='*60}")
    
    try:
        # Initialize detector
        print(f"Initializing {detector_name}...")
        detector = detector_class(show_log=True)
        print(f"✓ {detector_name} initialized successfully")
        
        # Run detection
        print(f"Running detection on test image...")
        annotated_image, layout_output = detector.detect(test_image_path)
        print(f"✓ Detection completed successfully")
        
        # Print detection results
        print(f"Detection Results:")
        print(f"  - Number of detected elements: {len(layout_output.bboxes)}")
        
        for i, bbox in enumerate(layout_output.bboxes):
            print(f"  - Element {i+1}: {bbox.label} (confidence: {bbox.confidence:.3f if bbox.confidence else 'N/A'})")
        
        # Save visualization
        output_path = output_dir / f"{detector_name.lower()}_result.png"
        detector.visualize((annotated_image, layout_output), output_path)
        print(f"✓ Visualization saved to: {output_path}")
        
        return True, None
        
    except ImportError as e:
        error_msg = f"Import error: {str(e)}"
        print(f"✗ {detector_name} failed - {error_msg}")
        return False, error_msg
        
    except Exception as e:
        error_msg = f"Runtime error: {str(e)}"
        print(f"✗ {detector_name} failed - {error_msg}")
        return False, error_msg

def main():
    """Main test function."""
    print("Layout Detectors Comprehensive Test")
    print("=" * 60)
    
    # Setup paths
    test_dir = Path(__file__).parent
    test_image_path = test_dir / "assets" / "news_paper.jpg"
    output_dir = test_dir / "output"
    output_dir.mkdir(exist_ok=True)
    
    # Check test image exists
    if not test_image_path.exists():
        print(f"Error: Test image not found at {test_image_path}")
        return
    
    print(f"Test image: {test_image_path}")
    print(f"Output directory: {output_dir}")
    
    # Import layout detectors
    layout_detectors = []
    
    # Try to import each detector
    # try:
    #     from omnidocs.tasks.layout_analysis.extractors.doc_layout_yolo import YOLOLayoutDetector
    #     layout_detectors.append((YOLOLayoutDetector, "YOLOLayoutDetector"))
    # except ImportError:
    #     print("YOLOLayoutDetector not available")
    
    # try:
    #     from omnidocs.tasks.layout_analysis.extractors.florence import FlorenceLayoutDetector
    #     layout_detectors.append((FlorenceLayoutDetector, "FlorenceLayoutDetector"))
    # except ImportError:
    #     print("FlorenceLayoutDetector not available")
    
    # try:
    #     from omnidocs.tasks.layout_analysis.extractors.paddle import PaddleLayoutDetector
    #     layout_detectors.append((PaddleLayoutDetector, "PaddleLayoutDetector"))
    # except ImportError:
    #     print("PaddleLayoutDetector not available")
    
    # try:
    #     from omnidocs.tasks.layout_analysis.extractors.rtdetr import RTDETRLayoutDetector
    #     layout_detectors.append((RTDETRLayoutDetector, "RTDETRLayoutDetector"))
    # except ImportError:
    #     print("RTDETRLayoutDetector not available")
    
    try:
        from omnidocs.tasks.layout_analysis.extractors.surya import SuryaLayoutDetector
        layout_detectors.append((SuryaLayoutDetector, "SuryaLayoutDetector"))
    except ImportError:
        print("SuryaLayoutDetector not available")
    
    if not layout_detectors:
        print("No layout detectors available for testing!")
        return
    
    # Test each detector
    working_detectors = []
    failed_detectors = []
    
    for detector_class, detector_name in layout_detectors:
        success, error = test_layout_detector(detector_class, detector_name, test_image_path, output_dir)
        if success:
            working_detectors.append(detector_name)
        else:
            failed_detectors.append((detector_name, error))
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    print(f"Working detectors ({len(working_detectors)}):")
    for detector in working_detectors:
        print(f"  ✓ {detector}")
    
    print(f"\nFailed detectors ({len(failed_detectors)}):")
    for detector, error in failed_detectors:
        print(f"  ✗ {detector}: {error}")
    
    print(f"\nOutput files saved in: {output_dir}")

if __name__ == "__main__":
    main()
