import sys
import logging
from typing import Union, List, Dict, Any, Optional, Tuple, Sequence
from pathlib import Path
import cv2
import numpy as np
import torch
from huggingface_hub import snapshot_download
from PIL import Image , ImageDraw
from omnidocs.utils.logging import get_logger, log_execution_time
from omnidocs.tasks.layout_analysis.base import BaseLayoutDetector, BaseLayoutMapper
from omnidocs.tasks.layout_analysis.enums import LayoutLabel
from omnidocs.tasks.layout_analysis.models import LayoutBox, LayoutOutput
from omnidocs.utils.model_config import setup_model_environment


logger = get_logger(__name__)

# Setup model environment
_MODELS_DIR = setup_model_environment()

        
# ================================================================================================================
class FlorenceLayoutMapper(BaseLayoutMapper):
    """Label mapper for Florence layout detection model."""
    
    def _setup_mapping(self):
        mapping = {
            "cap": LayoutLabel.CAPTION,
            "footnote": LayoutLabel.TEXT,  # Map footnote to text since no direct equivalent
            "math": LayoutLabel.FORMULA,
            "list": LayoutLabel.LIST,
            "bottom": LayoutLabel.TEXT,  # Map page-footer to text
            "header": LayoutLabel.TEXT,  # Map page-header to text
            "picture": LayoutLabel.IMAGE,
            "section": LayoutLabel.TITLE,  # Map section-header to title
            "table": LayoutLabel.TABLE,
            "text": LayoutLabel.TEXT,
            "title": LayoutLabel.TITLE
        }
        self._mapping = {k.lower(): v for k, v in mapping.items()}
        self._reverse_mapping = {v: k for k, v in mapping.items()}

class FlorenceLayoutDetector(BaseLayoutDetector):
    """Florence-based layout detection implementation."""

    MODEL_REPO = "yifeihu/Florence-2-DocLayNet-Fixed"

    def __init__(
        self,
        device: Optional[str] = None,
        show_log: bool = False,
        trust_remote_code: bool = True,
        model_path: Optional[Union[str, Path]] = None,
        **kwargs
    ):
        """Initialize Florence Layout Detector."""
        super().__init__(show_log=show_log)

        # Initialize label mapper
        self._label_mapper = FlorenceLayoutMapper()

        if self.show_log:
            logger.info("Initializing FlorenceLayoutDetector")

        if device:
            self.device = device
        if self.show_log:
            logger.info(f"Using device: {self.device}")

        # Set default paths
        if model_path is None:
            model_path = _MODELS_DIR / "florence_layout" / self.MODEL_REPO.replace("/", "_")

        self.model_path = Path(model_path)
        self.trust_remote_code = trust_remote_code
        self.kwargs = kwargs

        # Check dependencies
        self._check_dependencies()

        # Download model if needed
        if not self._model_exists():
            if self.show_log:
                logger.info(f"Model not found at {self.model_path}, will download from HuggingFace")
            self._download_model()

        # Load model
        self._load_model()

    def _check_dependencies(self):
        """Check if required dependencies are available."""
        try:
            from transformers import AutoProcessor, AutoModelForCausalLM
        except ImportError as ex:
            logger.error("Failed to import transformers")
            raise ImportError(
                "transformers is not available. Please install it with: pip install transformers"
            ) from ex

    def _model_exists(self) -> bool:
        """Check if model files exist locally."""
        if not self.model_path.exists():
            return False

        # Check for essential files
        config_file = self.model_path / "config.json"
        model_file = self.model_path / "pytorch_model.bin"
        processor_file = self.model_path / "preprocessor_config.json"

        return config_file.exists() and (model_file.exists() or (self.model_path / "model.safetensors").exists()) and processor_file.exists()

    def _download_model(self) -> Path:
        """Download model from HuggingFace if it doesn't exist locally."""
        try:
            from transformers import AutoProcessor, AutoModelForCausalLM

            if self.show_log:
                logger.info(f"Downloading Florence model: {self.MODEL_REPO}")
                logger.info(f"Saving to: {self.model_path}")

            # Create model directory
            self.model_path.mkdir(parents=True, exist_ok=True)

            # Download and save processor
            if self.show_log:
                logger.info("Downloading processor...")
            processor = AutoProcessor.from_pretrained(
                self.MODEL_REPO,
                trust_remote_code=self.trust_remote_code
            )
            processor.save_pretrained(self.model_path)

            # Download and save model
            if self.show_log:
                logger.info("Downloading model...")
            model = AutoModelForCausalLM.from_pretrained(
                self.MODEL_REPO,
                trust_remote_code=self.trust_remote_code,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                **self.kwargs
            )
            model.save_pretrained(self.model_path)

            if self.show_log:
                logger.success(f"Model downloaded successfully to {self.model_path}")

            return self.model_path

        except Exception as e:
            logger.error("Error downloading Florence model", exc_info=True)
            # Clean up partial download
            if self.model_path.exists():
                import shutil
                shutil.rmtree(self.model_path)
            raise

    def _load_model(self) -> None:
        """Load Florence model from local path."""
        try:
            from transformers import AutoProcessor, AutoModelForCausalLM

            if self.show_log:
                logger.info("Loading Florence model from local path...")

            # Load model and processor from local path
            self.model = AutoModelForCausalLM.from_pretrained(
                str(self.model_path),
                trust_remote_code=self.trust_remote_code,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                **self.kwargs
            )
            self.processor = AutoProcessor.from_pretrained(
                str(self.model_path),
                trust_remote_code=self.trust_remote_code
            )
            self.model.to(self.device)

            if self.show_log:
                logger.success("Florence model loaded successfully")

        except Exception as e:
            logger.error("Failed to load Florence model", exc_info=True)
            raise

    def _download_model(self) -> Path:
        """
        Download model from remote source.
        Note: Handled by transformers library.
        """
        logger.info("Model downloading handled by transformers library")
        return None

    def _load_model(self) -> None:
        """
        Load the model into memory.
        Note: Model is loaded in __init__.
        """
        pass

    @log_execution_time
    def detect(
        self,
        input_path: Union[str, Path],
        max_new_tokens: int = 1024,
        do_sample: bool = False,
        num_beams: int = 3,
        **kwargs
    ) -> Tuple[Image.Image, LayoutOutput]:
        """Run layout detection with standardized labels."""
        try:
            # Load and preprocess input
            image = Image.open(input_path).convert("RGB")
            
            # Prepare inputs
            prompt = "<OD>"
            inputs = self.processor(
                text=prompt,
                images=image,
                return_tensors="pt"
            ).to(self.device)
            
            # Generate predictions - Fixed method call
            with torch.no_grad():
                # Try different generation approaches
                try:
                    # Method 1: Direct generate call
                    generated_ids = self.model.generate(
                        input_ids=inputs["input_ids"],
                        pixel_values=inputs["pixel_values"],
                        max_new_tokens=max_new_tokens,
                        do_sample=do_sample,
                        num_beams=num_beams,
                        **kwargs
                    )
                except AttributeError:
                    # Method 2: Use language_model.generate if available
                    if hasattr(self.model, 'language_model'):
                        generated_ids = self.model.language_model.generate(
                            input_ids=inputs["input_ids"],
                            pixel_values=inputs["pixel_values"],
                            max_new_tokens=max_new_tokens,
                            do_sample=do_sample,
                            num_beams=num_beams,
                            **kwargs
                        )
                    elif hasattr(self.model, 'text_decoder'):
                        # Method 3: Use text_decoder if available
                        generated_ids = self.model.text_decoder.generate(
                            input_ids=inputs["input_ids"],
                            pixel_values=inputs["pixel_values"],
                            max_new_tokens=max_new_tokens,
                            do_sample=do_sample,
                            num_beams=num_beams,
                            **kwargs
                        )
                    else:
                        # Method 4: Forward pass approach
                        outputs = self.model(
                            input_ids=inputs["input_ids"],
                            pixel_values=inputs["pixel_values"]
                        )
                        # Get the logits and convert to token ids
                        logits = outputs.logits if hasattr(outputs, 'logits') else outputs[0]
                        generated_ids = torch.argmax(logits, dim=-1)
            
            # Decode and post-process
            generated_text = self.processor.batch_decode(
                generated_ids,
                skip_special_tokens=False
            )[0]
            
            # Post-process the generation
            try:
                parsed_result = self.processor.post_process_generation(
                    generated_text,
                    task="<OD>",
                    image_size=(image.width, image.height)
                )
            except Exception as e:
                logger.warning(f"Post-processing failed: {e}. Trying alternative approach.")
                # Alternative parsing if post_process_generation fails
                parsed_result = self._parse_florence_output(generated_text, image.size)
            
            # Convert to standard format
            layout_boxes = []
            
            # Handle different output formats
            if "<OD>" in parsed_result and isinstance(parsed_result["<OD>"], dict):
                bboxes = parsed_result["<OD>"].get("bboxes", [])
                labels = parsed_result["<OD>"].get("labels", [])
            else:
                # Fallback parsing
                bboxes, labels = self._extract_boxes_and_labels(parsed_result)
            
            for bbox, label in zip(bboxes, labels):
                mapped_label = self.map_label(label.lower() if isinstance(label, str) else str(label).lower())
                if mapped_label:
                    layout_boxes.append(
                        LayoutBox(
                            label=mapped_label,
                            bbox=[float(coord) for coord in bbox],
                            confidence=None  # Florence model doesn't provide confidence scores
                        )
                    )
            
            # Create annotated image
            annotated_img = image.copy()
            draw = ImageDraw.Draw(annotated_img)
            
            # Draw boxes and labels
            for box in layout_boxes:
                color = self.color_map.get(box.label, 'gray')
                coords = box.bbox
                draw.rectangle(coords, outline=color, width=3)
                draw.text((coords[0], coords[1]-20), box.label, fill=color)
            
            return annotated_img, LayoutOutput(bboxes=layout_boxes)

        except Exception as e:
            logger.error("Error during prediction", exc_info=True)
            raise

    def _parse_florence_output(self, generated_text: str, image_size: Tuple[int, int]) -> Dict:
        """
        Alternative parsing method for Florence output.
        """
        try:
            # Basic parsing logic for Florence output format
            # This is a simplified parser - you might need to adjust based on actual output format
            result = {"<OD>": {"bboxes": [], "labels": []}}
            
            # Look for patterns in the generated text
            # Florence typically outputs in a structured format
            lines = generated_text.split('\n')
            for line in lines:
                if 'bbox' in line.lower() and 'label' in line.lower():
                    # Extract bbox and label information
                    # This is a placeholder - adjust based on actual output format
                    pass
            
            return result
        except Exception as e:
            logger.warning(f"Failed to parse Florence output: {e}")
            return {"<OD>": {"bboxes": [], "labels": []}}

    def _extract_boxes_and_labels(self, parsed_result: Any) -> Tuple[List, List]:
        """
        Extract bounding boxes and labels from parsed result.
        """
        bboxes = []
        labels = []
        
        try:
            if isinstance(parsed_result, dict):
                # Try different key combinations
                for key in ["<OD>", "od", "object_detection"]:
                    if key in parsed_result:
                        data = parsed_result[key]
                        if isinstance(data, dict):
                            bboxes = data.get("bboxes", data.get("boxes", []))
                            labels = data.get("labels", data.get("classes", []))
                            break
        except Exception as e:
            logger.warning(f"Failed to extract boxes and labels: {e}")
        
        return bboxes, labels

    def visualize(
        self,
        detection_result: Tuple[Image.Image, LayoutOutput],
        output_path: Union[str, Path],
    ) -> None:
        """
        Save annotated image and layout data to files.
        
        Args:
            detection_result: Tuple containing (PIL Image, LayoutOutput)
            output_path: Path to save visualization
        """
        super().visualize(detection_result, output_path)