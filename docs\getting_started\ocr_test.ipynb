{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b6f615e", "metadata": {}, "outputs": [], "source": ["from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor\n", "from omnidocs.tasks.ocr_extraction.extractors.surya_ocr import SuryaOCRExtractor"]}, {"cell_type": "code", "execution_count": null, "id": "3c009f75", "metadata": {}, "outputs": [], "source": ["def test_ocr_extraction():\n", "    from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor\n", "    from omnidocs.tasks.ocr_extraction.extractors.surya_ocr import SuryaOCRExtractor\n", "    \n", "    extractors = [PaddleOCRExtractor, TesseractOCRExtractor, EasyOCRExtractor, SuryaOCRExtractor]\n", "    image_path = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\CogLab\\\\11-07-2025\\\\Omnidocs\\\\tests\\\\ocr_extraction\\\\assets\\\\invoice.jpg\"\n", "    \n", "    for extractor_cls in extractors:\n", "        print(f\"\\nTesting {extractor_cls.__name__}\")\n", "        print(\"-\" * 40)\n", "        \n", "        try:\n", "            extractor = extractor_cls()\n", "            result = extractor.extract(image_path)\n", "            print(f\"Text length: {len(result.full_text)} chars\")\n", "        \n", "            vis_path = f\"visualized_{extractor_cls.__name__}.png\"\n", "            extractor.visualize(result, image_path, vis_path)\n", "\n", "\n", "            #load and visualize, if already saved as json\n", "            #extractor.visualize_from_json(\"image.jpg\", \"results.json\", \"viz.png\")\n", "\n", "\n", "            # with custom styling\n", "            extractor.visualize(\n", "                result, \n", "                image_path, \n", "                f\"styled_{extractor_cls.__name__}.png\",\n", "                box_color='green',\n", "                box_width=3,\n", "                show_text=True,\n", "                text_color='red'\n", "            )\n", "            \n", "            print(\"SUCCESS\")\n", "        except Exception as e:\n", "            print(f\"ERROR: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2a1bc4dc", "metadata": {}, "outputs": [], "source": ["test_ocr_extraction()\n"]}, {"cell_type": "code", "execution_count": null, "id": "a9ba62bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "final", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}