#!/usr/bin/env python3
"""
Test file for all Surya extractors (Math, Table, Text).
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from omnidocs.utils.logging import get_logger

logger = get_logger(__name__)

def test_surya_math_extractor():
    """Test Surya Math Extractor."""
    print("\n" + "="*60)
    print("Testing SuryaMathExtractor")
    print("="*60)
    
    try:
        from omnidocs.tasks.math_expression_extraction.extractors import SuryaMathExtractor
        
        # Initialize extractor
        print("Initializing SuryaMathExtractor...")
        extractor = SuryaMathExtractor(show_log=True)
        print("✓ SuryaMathExtractor initialized successfully")
        
        # Test with a sample image (you'd need to provide an actual image with math)
        test_image_path = "tests\math_expression_extraction\\assets\math_equation.png"
        if Path(test_image_path).exists():
            print(f"Testing extraction on: {test_image_path}")
            result = extractor.extract(test_image_path)
            print(f"✓ Extraction completed")
            print(f"  - Found {len(result.expressions)} math expressions")
            for i, expr in enumerate(result.expressions[:10]):  # Show first 3
                print(f"  - Expression {i+1}: {expr[:100]}...")
        else:
            print(f"Test image not found at {test_image_path}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_surya_table_extractor():
    """Test Surya Table Extractor."""
    print("\n" + "="*60)
    print("Testing SuryaTableExtractor")
    print("="*60)
    
    try:
        from omnidocs.tasks.table_extraction.extractors import SuryaTableExtractor
        
        # Initialize extractor
        print("Initializing SuryaTableExtractor...")
        extractor = SuryaTableExtractor(show_log=True)
        print("✓ SuryaTableExtractor initialized successfully")
        
        # Test with a sample image
        test_image_path = "tests\\table_extraction\\assets\\table_image.png"
        if Path(test_image_path).exists():
            print(f"Testing extraction on: {test_image_path}")
            result = extractor.extract(test_image_path)
            print(f"✓ Extraction completed")
            print(f"  - Found {len(result.tables)} tables")
            for i, table in enumerate(result.tables):
                print(f"  - Table {i+1}: {table.num_rows} rows, {table.num_cols} cols, {len(table.cells)} cells")
        else:
            print(f"Test image not found at {test_image_path}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_surya_text_extractor():
    """Test Surya Text Extractor."""
    print("\n" + "="*60)
    print("Testing SuryaTextExtractor")
    print("="*60)
    
    try:
        from omnidocs.tasks.text_extraction.extractors import SuryaTextExtractor
        
        # Initialize extractor
        print("Initializing SuryaTextExtractor...")
        extractor = SuryaTextExtractor(show_log=True)
        print("✓ SuryaTextExtractor initialized successfully")
        
        # Test with a sample image
        test_image_path = "tests/layout_detectors/assets/news_paper.png"
        if Path(test_image_path).exists():
            print(f"Testing extraction on: {test_image_path}")
            result = extractor.extract(test_image_path)
            print(f"✓ Extraction completed")
            print(f"  - Found {len(result.text_blocks)} text blocks")
            print(f"  - Total text length: {len(result.full_text)} characters")
            if result.text_blocks:
                print(f"  - First block: {result.text_blocks[0].text[:100]}...")
        else:
            print(f"Test image not found at {test_image_path}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    """Main test function."""
    print("Surya Extractors Comprehensive Test")
    print("=" * 60)
    
    results = {}
    
    # Test each extractor
    results['math'] = test_surya_math_extractor()
    results['table'] = test_surya_table_extractor()
    results['text'] = test_surya_text_extractor()
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    working = [name for name, success in results.items() if success]
    failed = [name for name, success in results.items() if not success]
    
    print(f"Working extractors ({len(working)}):")
    for extractor in working:
        print(f"  ✓ Surya{extractor.title()}Extractor")
    
    print(f"\nFailed extractors ({len(failed)}):")
    for extractor in failed:
        print(f"  ✗ Surya{extractor.title()}Extractor")

if __name__ == "__main__":
    main()
