[project]
name = "omnidocs"
version = "0.1.2"
description = "Deep document processing toolkit with modular components"
authors = [{name = "adithya-s-kolavi", email = "adithyas<PERSON><PERSON><EMAIL>"}]
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = [
    "pydantic>=2.8",
    "rich>=13.9.4",
    "pandas>=2.2.2",
    "Pillow>=10.4.0",
    "huggingface-hub>=0.26.2",
    "setuptools>=80.9.0",
    "easyocr==1.7.2",
    "pytesseract==0.3.13",
    "surya-ocr==0.14.6",
    "protobuf==3.20.2",
    "paddleocr==2.8.1",
    "paddlepaddle==2.6.1",
    "torch==2.7.1",
    "torchvision==0.22.1",
    "transformers==4.53.2",
    "numpy==1.26.4",
    "timm==1.0.17",
    "PyMuPDF==1.26.3",
    "PyPDF2==3.0.1",
    "pdftext==0.6.3",
    "docling==2.41.0",
    "pdfplumber==0.11.7",
    "camelot-py==1.0.0",
    "tabula-py==2.10.0",
    "opencv-python>=4.8.0",
    "pdf2image>=1.17.0",
    "doclayout-yolo>=0.0.3",
    "iopath>=0.1.9",
    "einops>=0.8.0",
    "omegaconf",
    "sentencepiece",
    "albumentations",
    "webdataset",
    "ftfy",
    "hf_xet"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
